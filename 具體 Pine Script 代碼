//@version=6
strategy("進階MACD策略 - 20倍槓桿全倉", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=100, margin_long=5, margin_short=5)

// ==================== 輸入參數設定 ====================

// MACD 指標參數
fastLength = input.int(12, "快線週期", minval=1, group="MACD設定")
slowLength = input.int(26, "慢線週期", minval=1, group="MACD設定")
macdLength = input.int(9, "MACD信號線週期", minval=1, group="MACD設定")

// 出場條件參數 (針對20倍槓桿優化)
stopLossPercent = input.float(1.0, "止損百分比 (%)", minval=0.1, maxval=5, step=0.1, group="出場設定")
takeProfitPercent = input.float(2.0, "止盈百分比 (%)", minval=0.1, maxval=10, step=0.1, group="出場設定")
useReverseSignalExit = input.bool(true, "使用反向信號出場", group="出場設定")
useTrailingStop = input.bool(true, "使用追蹤止損", group="出場設定")
trailingStopPercent = input.float(0.5, "追蹤止損百分比 (%)", minval=0.1, maxval=2, step=0.1, group="出場設定")

// 風險管理參數 (20倍槓桿全倉設定)
positionSizeType = input.string("全倉", "倉位大小類型", options=["全倉", "固定百分比", "ATR動態"], group="風險管理")
fixedPercent = input.float(100, "固定百分比 (%)", minval=1, maxval=100, step=1, group="風險管理")
atrPeriod = input.int(14, "ATR週期", minval=1, group="風險管理")
atrMultiplier = input.float(1.5, "ATR倍數", minval=0.5, maxval=3, step=0.1, group="風險管理")
maxDrawdownPercent = input.float(20, "最大回撤百分比 (%)", minval=1, maxval=50, step=1, group="風險管理")
leverageMultiplier = input.float(20, "槓桿倍數", minval=1, maxval=100, step=1, group="風險管理")

// ==================== 指標計算 ====================

// MACD 計算
macdLine = ta.ema(close, fastLength) - ta.ema(close, slowLength)
signalLine = ta.ema(macdLine, macdLength)
histogram = macdLine - signalLine

// ATR 計算（用於動態倉位管理）
atr = ta.atr(atrPeriod)

// 回撤計算
equity = strategy.equity
maxEquity = ta.highest(equity, 252) // 過去一年的最高淨值
currentDrawdown = (maxEquity - equity) / maxEquity * 100

// ==================== 倉位大小計算 ====================

// 計算倉位大小 (針對20倍槓桿優化)
var float positionSize = na
switch positionSizeType
    "全倉" => positionSize := 100  // 使用全部權益
    "固定百分比" => positionSize := fixedPercent
    "ATR動態" =>
        // 基於ATR的動態倉位管理，考慮槓桿風險
        riskAmount = strategy.equity * 0.02  // 每次交易風險2%
        stopDistance = atr * atrMultiplier
        basePosition = riskAmount / stopDistance / close * 100
        positionSize := math.min(basePosition, 100)  // 最大不超過100%

// 回撤限制檢查
drawdownOK = currentDrawdown < maxDrawdownPercent

// ==================== 交易信號 ====================

// 進場信號
longSignal = ta.crossover(histogram, 0) and drawdownOK
shortSignal = ta.crossunder(histogram, 0) and drawdownOK

// 反向信號（用於出場）
longExitSignal = ta.crossunder(histogram, 0)
shortExitSignal = ta.crossover(histogram, 0)

// ==================== 交易執行 ====================

// 進場 (全倉交易)
if longSignal
    strategy.entry("做多", strategy.long, qty=positionSize, comment="MACD做多-全倉")

if shortSignal
    strategy.entry("做空", strategy.short, qty=positionSize, comment="MACD做空-全倉")

// 出場 - 止損止盈 (針對20倍槓桿優化)
if strategy.position_size > 0  // 多頭倉位
    if useTrailingStop
        strategy.exit("多頭出場", "做多", stop=close * (1 - stopLossPercent/100), limit=close * (1 + takeProfitPercent/100), trail_percent=trailingStopPercent)
    else
        strategy.exit("多頭出場", "做多", stop=close * (1 - stopLossPercent/100), limit=close * (1 + takeProfitPercent/100))

if strategy.position_size < 0  // 空頭倉位
    if useTrailingStop
        strategy.exit("空頭出場", "做空", stop=close * (1 + stopLossPercent/100), limit=close * (1 - takeProfitPercent/100), trail_percent=trailingStopPercent)
    else
        strategy.exit("空頭出場", "做空", stop=close * (1 + stopLossPercent/100), limit=close * (1 - takeProfitPercent/100))

// 反向信號出場
if useReverseSignalExit
    if strategy.position_size > 0 and longExitSignal
        strategy.close("做多", comment="反向信號出場")
    if strategy.position_size < 0 and shortExitSignal
        strategy.close("做空", comment="反向信號出場")

// ==================== 視覺化 ====================

// 繪製MACD指標
hline(0, "零軸", color=color.gray, linestyle=hline.style_dashed)
plot(histogram, title="MACD柱狀圖", color=histogram >= 0 ? color.green : color.red, style=plot.style_histogram)
plot(macdLine, title="MACD線", color=color.blue, linewidth=1)
plot(signalLine, title="信號線", color=color.orange, linewidth=1)

// 標記交易信號
plotshape(longSignal, title="做多信號", location=location.belowbar, color=color.green, style=shape.triangleup, size=size.small)
plotshape(shortSignal, title="做空信號", location=location.abovebar, color=color.red, style=shape.triangledown, size=size.small)

// 顯示風險資訊 (20倍槓桿監控)
var table infoTable = table.new(position.top_right, 2, 6, bgcolor=color.white, border_width=1)
if barstate.islast
    table.cell(infoTable, 0, 0, "當前回撤", text_color=color.black)
    table.cell(infoTable, 1, 0, str.tostring(currentDrawdown, "#.##") + "%", text_color=currentDrawdown > maxDrawdownPercent ? color.red : color.green)
    table.cell(infoTable, 0, 1, "倉位大小", text_color=color.black)
    table.cell(infoTable, 1, 1, str.tostring(positionSize, "#.##") + "%", text_color=color.black)
    table.cell(infoTable, 0, 2, "槓桿倍數", text_color=color.black)
    table.cell(infoTable, 1, 2, str.tostring(leverageMultiplier, "#") + "x", text_color=color.orange)
    table.cell(infoTable, 0, 3, "ATR", text_color=color.black)
    table.cell(infoTable, 1, 3, str.tostring(atr, "#.####"), text_color=color.black)
    table.cell(infoTable, 0, 4, "倉位狀態", text_color=color.black)
    positionText = strategy.position_size > 0 ? "做多-全倉" : strategy.position_size < 0 ? "做空-全倉" : "無倉位"
    table.cell(infoTable, 1, 4, positionText, text_color=strategy.position_size != 0 ? color.blue : color.gray)
    table.cell(infoTable, 0, 5, "風險等級", text_color=color.black)
    riskLevel = currentDrawdown > 15 ? "高風險" : currentDrawdown > 10 ? "中風險" : "低風險"
    riskColor = currentDrawdown > 15 ? color.red : currentDrawdown > 10 ? color.orange : color.green
    table.cell(infoTable, 1, 5, riskLevel, text_color=riskColor)